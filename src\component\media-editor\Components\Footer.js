import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Pressable,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { hp, wp } from '../utils/Constants/dimensionUtils';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import { requestCameraPermission } from '../utils/MediaPermissions';

const Footer = ({
  onpresscamera,
  onPressAa,
  Camera,
  onPressEdit,
  onPressFlip,
  onPressProfile,
  Aastyle,
  AaTextstyle,
  activeColor,
  isActiveInput,
  isDrawing,
}) => {
  const [recentImage, setRecentImage] = useState(null);

  useEffect(() => {
    const getRecentImage = async () => {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
        );
        if (granted !== PermissionsAndroid.RESULTS.GRANTED) return;
      }

      const photos = await CameraRoll.getPhotos({
        first: 1,
        assetType: 'Photos',
      });
      // console.log('photo!!!!!!!!!',photos);

      if (photos.edges.length > 0) {
        setRecentImage(photos.edges[0].node.image.uri);
      }
    };

    getRecentImage();
  }, []);

  const onPressCameraHandler = () => {
    const isCameraAllowed = requestCameraPermission();
    if (isCameraAllowed) {
      onpresscamera();
    }
  };

  return (
    <View
      style={{
        height: Camera ? hp(12) : hp(8.5),
        width: '100%',
        backgroundColor: 'white',
        borderTopRightRadius: hp(2),
        borderTopLeftRadius: hp(2),
        justifyContent: 'center',
        // flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: wp(3),
        zIndex: 100001,
      }}
    >
      {/* <TouchableOpacity
        onPress={onPressProfile}
        disabled={isActiveInput || activeColor || isDrawing}
      >
        <Image
          source={recentImage ? { uri: recentImage } : require('../Assets/Images/Profileimg.png')}
          style={{ height: hp(3.5), width: hp(3.5), borderRadius: 20 }}
        />
      </TouchableOpacity> */}
      {/* {Camera ? ( */}
      {/* <Pressable style={styles.camerabox} onPress={onPressCameraHandler}>
          <Image
            source={require('../Assets/Images/Camera.png')}
            style={{ height: hp(6), width: hp(6) }}
          />
          <Text style={[styles.cameratxt, { alignSelf: 'center', paddingVertical: 7 }]}>Aa</Text>
        </Pressable> */}
      {/* ) : ( */}
      {/* <TouchableOpacity style={[styles.Aa, Aastyle]} onPress={onPressAa} disabled={isDrawing}>
          {activeColor ? (
            <Image
              source={require('../Assets/Images/Vector.png')}
              style={{ height: 20, width: 20 }}
            />
          ) : (
            <Text style={[styles.cameratxt, AaTextstyle, { color: isDrawing ? 'grey' : 'black' }]}>
              Aa
            </Text>
          )}
        </TouchableOpacity> */}
      {/* )} */}

      <TouchableOpacity
        style={{ height: 35, width: 35 }}
        onPress={onPressEdit}
        disabled={isActiveInput || activeColor || isDrawing}
      >
        <Image
          source={
            Camera ? require('../Assets/Images/Flip.png') : require('../Assets/Images/Editicon.png')
          }
          style={[
            styles.EditIcon,
            { tintColor: isActiveInput || activeColor || isDrawing ? 'grey' : '#6A4DBB' },
          ]}
        />
      </TouchableOpacity>
    </View>
  );
};
const styles = StyleSheet.create({
  cameratxt: {
    fontSize: 24,
    fontWeight: '700',
  },
  camerabox: {
    backgroundColor: 'white',
    borderRadius: hp(3.7),
    padding: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
    bottom: hp(4),
  },
  Aa: {
    height: 50,
    width: 50,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  EditIcon: {
    height: 27,
    width: 27,
  },
});
export default Footer;
