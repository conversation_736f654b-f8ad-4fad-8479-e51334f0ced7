import React, { FC, useEffect, useState } from 'react';

import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { ActivityIndicator, Image, TouchableOpacity, View } from 'react-native';

import { colors } from '../theme/colors';
import { AppStyles } from '../theme/appStyles';
import { IMAGES } from '../assets/Images';

import { getAsyncToken } from '../utils/asyncStorage';
import { SCREENS } from './screenNames';

// Screens
import HomeScreen from '../screens/Home/HomeScreen';
import SetUsernameScreen from '../screens/Auth/SetUsernameScreen';
import SetProfileScreen from '../screens/Auth/SetProfileScreen';
import SignupScreen from '../screens/Auth/SignupScreen';

import ScanContact from '../screens/Contacts/ScanContact';
import ContactsScreen from '../screens/Contacts/ContactsScreen';
import MyTabbar from '../component/MyTabbar';
import NewMessageScreen from '../screens/Home/Menu/NewMessageScreen';
import ChatSpecificScreen from '../screens/Home/Chats/ChatSpecificScreen';
import ArchiveScreen from '../screens/Home/ArchiveScreen';
import ProfileScreen from '../screens/Profile/ProfileScreen';
import CreateGroupScreen from '../screens/Home/Groups/CreateGroupScreen';
import CreateChannelScreen from '../screens/Home/Channels/CreateChannelScreen';

import StreamScreen from '../screens/Home/Channels/liveStreams/StreamScreen';
import ViewerStreamScreen from '../screens/Home/Channels/liveStreams/ViewerStreamScreen';
import ExploreScreen from '../screens/Home/Channels/ExploreScreen';
import PersonalProfileScreen from '../screens/Home/Chats/Screens/PersonalProfileScreen';
import ForwardMessagesScreen from '../component/PersonalChat/ForwardMessagesScreen';
import TranslationMessages from '../screens/Home/Chats/Screens/TranslationMessages';
import ChannelsScreen from '../screens/Home/Channels/ChannelsScreen';
// import AppLanguageScreen from '../screens/Profile/Settings/AppLanguageScreen';

import ScheduleLiveStreamScreen from '../screens/Home/Channels/Screens/ScheduleLiveStream';
import ChannelProfileScreen from '../screens/Home/Channels/ChannelProfileScreen';
import GroupAnalyticsScreen from '../screens/Home/Groups/GroupAnalyticsScreen';
import DummyScreen from '../screens/Home/Channels/Screens/DummyScreen';
import AddChatspaceMembers from '../screens/Home/Groups/AddChatspaceMembers';
import GroupPermissions from '../screens/Home/Groups/GroupPermissions';
import GoogleMapsScreen from '../screens/Home/Chats/Screens/GoogleMapsScreen';
import LiveLocationSharingScreen from '../screens/Home/Chats/Screens/LiveLocationSharingScreen';
import HelpScreen from '../screens/Profile/Settings/HelpScreen';
import FAQScreen from '../screens/Profile/Settings/Help/FAQScreen';
import TermsPrivacyScreen from '../screens/Profile/Settings/Help/TermsPrivacyScreen';
import AppInfoScreen from '../screens/Profile/Settings/Help/AppInfoScreen';
import ContactUsScreen from '../screens/Profile/Settings/Help/ContactUsScreen';
import AccountScreen from '../screens/Profile/Settings/AccountScreen';
import EmailScreen from '../screens/Profile/Settings/Account/EmailScreen';
import NameProfileColoursScreen from '../screens/Profile/Settings/Account/NameProfileColoursScreen';
import SecurityScreen from '../screens/Profile/Settings/Account/SecurityScreen';
import Enable2FAScreen from '../screens/Profile/Settings/Account/Security/Enable2FAScreen';
import SetSecurityPinScreen from '../screens/Profile/Settings/Account/Security/SetSecurityPinScreen';
import ChangeSecurityPinScreen from '../screens/Profile/Settings/Account/Security/ChangeSecurityPinScreen';
import VerifyEmailScreen from '../screens/Profile/Settings/Account/EmailSteps/VerifyEmailScreen';
import GroupSettingsScreen from '../screens/Home/Groups/GroupSettingsScreen';
import MapScreen from '../component/Mapbox';

import DocumentPreviewScreen from '../screens/Home/Chats/Screens/DocumentPreviewScreen';
import AdminsScreen from '../screens/Home/Groups/AdminsScreen';
import AdminPermissions from '../screens/Home/Groups/AdminPermissions';
import GroupInviteScreen from '../screens/Home/Groups/GroupInviteScreen';
import EditGroupInfo from '../screens/Home/Groups/EditGroupInfo';
import NotificationScreen from '../screens/Profile/Settings/NotificationScreen';
import FavouriteScreen from '../screens/Profile/Settings/FavouriteScreen';
import LiveStreamFeedsScreen from '../screens/Home/Channels/Screens/LiveStreamFeedsScreen';
import MyChannelsScreen from '../screens/Home/Channels/Screens/MyChannelsScreen';
import ContactPickerScreen from '../screens/Home/Chats/Screens/ContactPickerScreen';
import ViewContactScreen from '../screens/Home/Chats/ViewContactScreen';
import ChatSettingsScreen from '../screens/Profile/Settings/ChatSettingsScreen';
import ChatWallpaperScreen from '../screens/Profile/Settings/Chat/ChatWallpaperScreen';
import TranslateLanguageScreen from '../screens/Profile/Settings/Chat/TranslateLanguageScreen';
import ChatBackupScreen from '../screens/Profile/Settings/Chat/ChatBackupScreen';
import StorageAndDataScreen from '../screens/Profile/Settings/Chat/StorageAndDataScreen';
import MediaPreviewScreen from '../screens/Home/components/MediaPreviewScreen';
import PrivacySettingsScreen from '../screens/Profile/Settings/PrivacySettingsScreen';
import LastSeenScreen from '../screens/Profile/Settings/Privacy/LastSeenScreen';
import ProfilePhotoScreen from '../screens/Profile/Settings/Privacy/ProfilePhotoScreen';
import BioScreen from '../screens/Profile/Settings/Privacy/BioScreen';
import BlockedPeopleScreen from '../screens/Profile/Settings/Privacy/BlockedPeopleScreen';
import DefaultMessageTimerScreen from '../screens/Profile/Settings/Privacy/DefaultMessageTimerScreen';
import PhoneNumberScreen from '../screens/Profile/Settings/Privacy/PhoneNumberScreen';
import ForwardMessageScreen from '../screens/Profile/Settings/Privacy/ForwardMessageScreen';
import GroupChannelInvitationScreen from '../screens/Profile/Settings/Privacy/GroupChannelInvitationScreen';
import MessagesScreen from '../screens/Profile/Settings/Privacy/MessagesScreen';
import ImagesScreen from '../screens/Profile/Settings/Privacy/ImagesScreen';
import DocumentsScreen from '../screens/Profile/Settings/Privacy/DocumentsScreen';
import ContactsExceptLastSeen from '../screens/Profile/Settings/Privacy/Last Seen/ContactsExceptLastSeen';
import ContactsExceptPhoneNumber from '../screens/Profile/Settings/Privacy/Phone Number/ContactsExceptPhoneNumber';
import AppLanguageScreen from '../screens/Profile/Settings/AppLanguageScreen';
import { FontAwesome6Icons } from '../utils/vectorIcons';
import SavePostScreen from '../screens/Profile/SavePostScreen';
import DisappearingMessagesScreen from '../screens/Profile/Screens/DisappearingMessagesScreen';

import NewCallScreen from '../screens/Call/NewCallScreen';
import ScheduleCallScreen from '../screens/Call/ScheduleCallScreen';
import MissCallScreen from '../screens/Auth/MissCallScreen';
import MainCallScreen from '../screens/Call/MainCallScreen';

import CallScreen from '../screens/Call/CallScreen';
import PremiumFeaturesScreen from '../screens/Profile/Settings/PremiumFeaturesScreen';
import VoiceSampleScreen from '../screens/Profile/Settings/PremiumFeats/VoiceSampleScreen';
// import CameraScreen from '../screens/Home/components/CameraScreen';
import IncomingCallScreen from '../screens/Call/IncomingCallScreen';
import { Client } from '../lib/Client';
import GroupMembersScreen from '../screens/Home/Groups/GroupMembersScreen';
import EditChannelInfo from '../screens/Home/Channels/Screens/EditChannelInfo';
import FollowersScreen from '../screens/Home/Channels/Screens/FollowerScreen';
import BMainScreen from '../BlindMode/Screens/BMainScreen';
import { BTabNavigator } from '../BlindMode/Navigation/BNavigator';
import GroupCallScreen from '../screens/Call/GroupCallScreen';
import GroupRequestsScreen from '../screens/Home/Groups/GroupRequestsScreen';
import BScanScreen from '../BlindMode/Screens/BScanScreen';
import MediaEditor from '../component/media-editor/Screens/MediaEditorScreen';
import {
  createStackNavigator,
  TransitionPresets,
  StackNavigationOptions,
  CardStyleInterpolators,
  HeaderStyleInterpolators,
} from '@react-navigation/stack';

import {
  createNativeStackNavigator,
  NativeStackNavigationOptions,
} from '@react-navigation/native-stack';
const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

const headerStyleTransparent: StackNavigationOptions = {
  headerStyle: {
    backgroundColor: colors._7A5DCB_purple,
    shadowOpacity: 0,
    elevation: 0,
  },
  headerTransparent: true,
  headerTitleAlign: 'center',
  // ...TransitionPresets.SlideFromRightIOS,
};

const BackHeader = ({ navigation, screenName }: any) => {
  return (
    <TouchableOpacity
      onPress={() => {
        if (screenName === SCREENS.LoginScreen) {
          navigation.navigate(SCREENS.IntroScreen);
        } else if (screenName === SCREENS.SignupScreen) {
          navigation.navigate(SCREENS.SignupScreen);
        } else {
          navigation.goBack();
        }
      }}
      style={AppStyles.headerBackView}
    >
      <FontAwesome6Icons name="arrow-right-long" size={20} color="#FFFFFF" />
    </TouchableOpacity>
  );
};

const StackNavigator: FC = () => {
  const [initialScreen, setInitialScreen] = useState<string | null>(null);
  useEffect(() => {
    const initialize = async () => {
      const token = await Client.AuthToken.get();
      if (token) {
        setInitialScreen(SCREENS.HomeScreen);
      } else {
        setInitialScreen(SCREENS.SignupScreen);
      }
    };
    initialize();
  }, []);
  if (!initialScreen) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator color={'#FFFFFF'} size="large" />
      </View>
    );
  }

  const fastTransition = {
    animation: 'timing' as const,
    config: {
      duration: 150,
    },
  };

  const screenOptions: NativeStackNavigationOptions = {
    gestureDirection: 'horizontal' as const,

    headerShown: false,
    animation: 'simple_push',
    animationDuration: 1,
    animationTypeForReplace: 'push',
  };

  return (
    <Stack.Navigator initialRouteName={initialScreen} screenOptions={screenOptions}>
      {/* ============== Auth screens ===============  */}
      <Stack.Screen name={SCREENS.SignupScreen} component={SignupScreen} />
      <Stack.Screen name={SCREENS.IncomingCallScreen} component={IncomingCallScreen} />
      <Stack.Screen
        name={SCREENS.MissCallScreen}
        component={MissCallScreen}
        options={({ navigation }) => ({
          // ...headerStyleTransparent,
          title: '',
          headerLeft: () => (
            <BackHeader navigation={navigation} screenName={SCREENS.SignupScreen} />
          ),
        })}
      />
      <Stack.Screen
        name={SCREENS.SetUsernameScreen}
        component={SetUsernameScreen}
        options={({ navigation }) => ({
          // ...headerStyleTransparent,
          headerShown: false,
        })}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={SCREENS.SetProfileScreen}
        component={SetProfileScreen}
      />
      {/* ============== Auth screens end ===============  */}

      {/* ============== BlindMode screens ===============  */}

      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={'BlindMainScreen'}
        component={BMainScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={'BScanScreen'}
        component={BScanScreen}
      />

      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={'BlindHomeScreen'}
        component={BTabNavigator}
      />

      {/* ============== BlindMode screens end ===============  */}
      <Stack.Screen
        name={SCREENS.CreateGroupScreen}
        component={CreateGroupScreen}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />

      <Stack.Screen
        name={SCREENS.SavePostScreen}
        component={SavePostScreen}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen
        name={SCREENS.AdminsScreen}
        component={AdminsScreen}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen
        name={SCREENS.AdminPermissions}
        component={AdminPermissions}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen
        name={SCREENS.GroupInviteScreen}
        component={GroupInviteScreen}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen
        name={SCREENS.AddChatspaceMembers}
        component={AddChatspaceMembers}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen
        name={SCREENS.EditGroupInfo}
        component={EditGroupInfo}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen
        name={SCREENS.GroupRequestsScreen}
        component={GroupRequestsScreen}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen
        name={SCREENS.GroupPermissions}
        component={GroupPermissions}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen
        name={SCREENS.CreateChannelScreen}
        component={CreateChannelScreen}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen
        name={SCREENS.PersonalProfileScreen}
        component={PersonalProfileScreen}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen
        name={SCREENS.ChannelProfileScreen}
        component={ChannelProfileScreen}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen
        name={SCREENS.ForwardMessagesScreen}
        component={ForwardMessagesScreen}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen name={SCREENS.HomeScreen} component={MyTabs} options={{ headerShown: false }} />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
          // ...TransitionPresets.SlideFromRightIOS,
        })}
        name={SCREENS.NewCallScreen}
        component={NewCallScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
          // ...TransitionPresets.SlideFromRightIOS,
        })}
        name={SCREENS.ScheduleCallScreen}
        component={ScheduleCallScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
          // ...TransitionPresets.SlideFromRightIOS,
        })}
        name={SCREENS.GroupAnalyticsScreen}
        component={GroupAnalyticsScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
          //   ...TransitionPresets.SlideFromRightIOS,
        })}
        name={SCREENS.GroupSettingsScreen}
        component={GroupSettingsScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
          // ...TransitionPresets.SlideFromRightIOS,
        })}
        name={SCREENS.ScanContact}
        component={ScanContact}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
          // ...TransitionPresets.SlideFromRightIOS,
        })}
        name={SCREENS.MainCallScreen}
        component={MainCallScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
          // ...TransitionPresets.SlideFromRightIOS,
        })}
        name={SCREENS.GropuCallInfoScreen}
        component={GroupCallScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
          // ...TransitionPresets.SlideFromRightIOS,
        })}
        name={SCREENS.NewMessageScreen}
        component={NewMessageScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
          // ...TransitionPresets.SlideFromRightIOS,
        })}
        name={SCREENS.ExploreScreen}
        component={ExploreScreen}
      />
      <Stack.Screen name={SCREENS.ChatSpecificScreen} component={ChatSpecificScreen} />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={SCREENS.DisappearingMessagesScreen}
        component={DisappearingMessagesScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={SCREENS.TranslationMessages}
        component={TranslationMessages}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={SCREENS.GroupMembersScreen}
        component={GroupMembersScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={SCREENS.ArchiveScreen}
        component={ArchiveScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={SCREENS.ChannelStreamScreen}
        component={StreamScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={SCREENS.ViewerScreenView}
        component={ViewerStreamScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={'DummyScreen'}
        component={DummyScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={SCREENS.ScheduleLiveStreamScreen}
        component={ScheduleLiveStreamScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={SCREENS.GoogleMapsScreen}
        component={GoogleMapsScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={SCREENS.LiveLocationSharingScreen}
        component={LiveLocationSharingScreen}
      />
      <Stack.Screen
        name={SCREENS.HelpScreen}
        component={HelpScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.DocumentPreviewScreen}
        component={DocumentPreviewScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.ContactPickerScreen}
        component={ContactPickerScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.ViewContactScreen}
        component={ViewContactScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen name={'mapscreen'} component={MapScreen} />
      <Stack.Screen name={SCREENS.FAQScreen} component={FAQScreen} />
      <Stack.Screen name={SCREENS.TermsPrivacyScreen} component={TermsPrivacyScreen} />
      <Stack.Screen name={SCREENS.AppInfoScreen} component={AppInfoScreen} />
      <Stack.Screen name={SCREENS.ContactUsScreen} component={ContactUsScreen} />
      <Stack.Screen name={SCREENS.AccountScreen} component={AccountScreen} />
      <Stack.Screen name={SCREENS.EmailScreen} component={EmailScreen} />
      <Stack.Screen name={SCREENS.NameProfileColoursScreen} component={NameProfileColoursScreen} />
      <Stack.Screen name={SCREENS.SecurityScreen} component={SecurityScreen} />
      <Stack.Screen name={SCREENS.NotificationScreen} component={NotificationScreen} />
      <Stack.Screen name={SCREENS.FavouriteScreen} component={FavouriteScreen} />
      <Stack.Screen name={SCREENS.ChatSettingsScreen} component={ChatSettingsScreen} />
      <Stack.Screen name={SCREENS.ChatWallpaperScreen} component={ChatWallpaperScreen} />
      <Stack.Screen name={SCREENS.TranslateLanguageScreen} component={TranslateLanguageScreen} />
      <Stack.Screen name={SCREENS.ChatBackupScreen} component={ChatBackupScreen} />
      <Stack.Screen name={SCREENS.StorageAndDataScreen} component={StorageAndDataScreen} />
      <Stack.Screen name={SCREENS.VerifyEmailScreen} component={VerifyEmailScreen} />
      <Stack.Screen name={SCREENS.Enable2FAScreen} component={Enable2FAScreen} />
      <Stack.Screen name={SCREENS.SetSecurityPinScreen} component={SetSecurityPinScreen} />
      <Stack.Screen name={SCREENS.ChangeSecurityPinScreen} component={ChangeSecurityPinScreen} />
      <Stack.Screen name={SCREENS.AppLanguage} component={AppLanguageScreen} />
      <Stack.Screen name={SCREENS.LiveStreamFeeds} component={LiveStreamFeedsScreen} />
      <Stack.Screen name={SCREENS.MyChannelsScreen} component={MyChannelsScreen} />
      <Stack.Screen name={SCREENS.MediaPreviewScreen} component={MediaPreviewScreen} />
      <Stack.Screen name={SCREENS.MediaEditor} component={MediaEditor} />
      {/* <Stack.Screen name={SCREENS.CameraScreen} component={CameraScreen} /> */}
      <Stack.Screen
        name={SCREENS.PrivacySetting}
        component={PrivacySettingsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.EditChannelInfo}
        component={EditChannelInfo}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.FollowersScreen}
        component={FollowersScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.LastSeenScreen}
        component={LastSeenScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.ProfilePhotoScreen}
        component={ProfilePhotoScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.BioScreen}
        component={BioScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.BlockedPeopleScreen}
        component={BlockedPeopleScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.DefaultMessageTimerScreen}
        component={DefaultMessageTimerScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.PhoneNumberScreen}
        component={PhoneNumberScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.ForwardMessageScreen}
        component={ForwardMessageScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.GroupChannelInvitationScreen}
        component={GroupChannelInvitationScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.MessagesScreen}
        component={MessagesScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.ImagesScreen}
        component={ImagesScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.DocumentsScreen}
        component={DocumentsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.ContactsExceptLastSeen}
        component={ContactsExceptLastSeen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={SCREENS.ContactsExceptPhoneNumber}
        component={ContactsExceptPhoneNumber}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
          animation: 'fade',
        })}
        name={SCREENS.AppLanguageScreen}
        component={AppLanguageScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
        })}
        name={SCREENS.PremiumFeaturesScreen}
        component={PremiumFeaturesScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
          animation: 'slide_from_bottom',
        })}
        name={SCREENS.VoiceSampleScreen}
        component={VoiceSampleScreen}
      />
    </Stack.Navigator>
  );
};

function MyTabs() {
  return (
    <Tab.Navigator
      tabBar={(props) => <MyTabbar {...props} />}
      // initialRouteName={screenName.indiansPage}
      screenOptions={{
        headerShown: false,
        tabBarHideOnKeyboard: true,
        lazy: true,
      }}
    >
      <Tab.Screen
        options={({ navigation }) => ({
          tabBarLabel: 'Home',
          headerShown: false,
        })}
        name={SCREENS.HomeScreenTab}
        component={HomeScreen}
      />
      <Tab.Screen
        options={({ navigation }) => ({
          tabBarLabel: 'Contacts',
          headerShown: false,
        })}
        name={SCREENS.ContactsScreen}
        component={ContactsScreen}
      />

      {/* call Screens  */}
      <Tab.Screen
        options={({ navigation }) => ({
          tabBarLabel: 'Calls',
          title: '',
          headerStyle: {
            shadowColor: colors.black,
            elevation: 50,
          },
        })}
        name={SCREENS.CallScreen}
        component={CallScreen}
      />
      <Tab.Screen
        options={({ navigation }) => ({
          tabBarLabel: '',
          headerShown: false,
        })}
        name={SCREENS.ProfileScreen}
        component={ProfileScreen}
      />
    </Tab.Navigator>
  );
}

export default StackNavigator;
