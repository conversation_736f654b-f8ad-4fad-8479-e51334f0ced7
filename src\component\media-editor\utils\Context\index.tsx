import React, { createContext, useContext, useState } from "react";

// Define the shape of the context
interface DataContextType {
  memorizedPosition: any[];
  setMemorizedPosition: React.Dispatch<React.SetStateAction<{ type: string; id: string | number; x: number; y: number }[]>>;
}

// Create context with default values
const DataContext = createContext<DataContextType | undefined>(undefined);

// ✅ Wrap children properly inside the provider
export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [memorizedPosition, setMemorizedPosition] = useState<
    { type: string; id: string | number; x: number; y: number }[]
  >([])

  return (
    <DataContext.Provider value={{ memorizedPosition, setMemorizedPosition }}>
      {children}
    </DataContext.Provider>
  );
};

// ✅ Create a custom hook to use the context
export const useData = (): DataContextType => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error("useData must be used within a DataProvider");
  }
  return context;
};
