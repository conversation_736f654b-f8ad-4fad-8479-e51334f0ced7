import React, { useState, forwardRef, useImperativeHandle, useRef, useEffect } from 'react';
import { View, StyleSheet, Image, Text, TouchableOpacity } from 'react-native';
import { Canvas, Path, Picture, Skia } from '@shopify/react-native-skia';
import ViewShot from 'react-native-view-shot';
import { useData } from '../../utils/Context';
import { hp } from '../../utils/Constants/dimensionUtils';
import { updatePosition } from '../../utils/StateData';

interface IPath {
  id: number | string;
  path: any;
  color: string;
  size: number;
  isErase: boolean;
}

export interface PaintBrushRef {
  Undo: () => void;
  Redo: () => void;
  handleOkay: () => void;
  ToggleEraseMode: (value: boolean) => void;
}

interface PaintBrushProps {
  visible: boolean;
  paintData?: IPath[];
  strokeWidth?: number;
  eraserSize?: number;
  paintClr?: string;
  activeStoryIdx?: number;
  setImages?: any;
  onDrawStateChange?: (isDrawing: boolean) => void;
}

const PaintBrush = forwardRef<PaintBrushRef, PaintBrushProps>(
  (
    {
      visible,
      paintData = [],
      strokeWidth = 2,
      eraserSize = 10,
      paintClr = '#000',
      activeStoryIdx,
      setImages,
      onDrawStateChange,
    },
    ref,
  ) => {
    const viewShotRef = useRef(null);
    const [paths, setPaths] = useState<IPath[]>([]);
    const [currentPath, setCurrentPath] = useState<IPath | null>(null);
    const [eraseMode, setEraseMode] = useState(false);
    const [undoStack, setUndoStack] = useState<IPath[]>([]);
    const [redoStack, setRedoStack] = useState<IPath[]>([]);
    const { setMemorizedPosition } = useData();

    const activeTouchRef = useRef(null);
    const drawingRef = useRef(false);

    useEffect(() => {
      if (paintData && paintData.length > 0) {
        const initialPaths = paintData.map((p) => ({
          id: p.id,
          path: Skia.Path.MakeFromSVGString(p.segments?.join(' ') || '') || Skia.Path.Make(),
          color: p.color || paintClr,
          size: p.size || strokeWidth,
          isErase: p.isErase || false,
        }));
        setPaths(initialPaths);
      } else {
        setPaths([]);
      }
    }, [paintData]);

    const onTouchHandler = (e) => {
      if (!visible) return;

      const { touches, identifier, locationX: x, locationY: y } = e.nativeEvent;

      if (touches.length === 0) {
        if (activeTouchRef.current !== null && currentPath) {
          setPaths((prev) => {
            const updated = [...prev, currentPath];
            return updated;
          });
          setUndoStack([]);
          setRedoStack([]);
        }

        activeTouchRef.current = null;
        drawingRef.current = false;
        setCurrentPath(null);
        onDrawStateChange?.(false);
        return;
      }

      if (touches.length > 0) {
        if (activeTouchRef.current === null) {
          activeTouchRef.current = identifier;

          const newPath = Skia.Path.Make();
          newPath.moveTo(x, y);

          const newCurrentPath = {
            id: `${Date.now()}-${Math.random()}`,
            path: newPath,
            color: eraseMode ? 'transparent' : paintClr,
            size: eraseMode ? eraserSize : strokeWidth,
            isErase: eraseMode,
          };

          setCurrentPath(newCurrentPath);
          drawingRef.current = true;
          onDrawStateChange?.(true);
          return;
        }

        if (identifier === activeTouchRef.current && currentPath && drawingRef.current) {
          const newPath = currentPath.path.copy();
          newPath.lineTo(x, y);

          setCurrentPath({
            ...currentPath,
            path: newPath,
          });
        }
      }
    };

    const handleUndo = () => {
      setPaths((prevPaths) => {
        if (prevPaths.length === 0) return prevPaths;

        const newPaths = [...prevPaths];
        const lastPath = newPaths.pop();
        setUndoStack((prevUndo) => [...prevUndo, lastPath]);
        setRedoStack([]);
        return newPaths;
      });
    };

    const handleRedo = () => {
      setUndoStack((prevUndoStack) => {
        if (prevUndoStack.length === 0) return prevUndoStack;

        const newUndoStack = [...prevUndoStack];
        const pathToRedo = newUndoStack.pop();

        setPaths((prevPaths) => {
          const newPaths = [...prevPaths, pathToRedo];
          return newPaths;
        });
        setRedoStack((prevRedo) => [...prevRedo, pathToRedo]);
        return newUndoStack;
      });
    };

    const handleOkay = async () => {
      try {
        if (paths.length > 0) {
          const serializablePaths = paths.map((p) => ({
            id: p.id,
            segments: p.path.toSVGString().split(' '),
            color: p.color,
            size: p.size,
            isErase: p.isErase,
          }));

          // Update images state with new paths
          if (setImages && activeStoryIdx) {
            setImages((prevImages) =>
              prevImages.map((image) =>
                image.id === activeStoryIdx ? { ...image, path: serializablePaths } : image,
              ),
            );
          }

          // Save to memorizedPosition for position tracking
          const newPathEntry = {
            id: `${Date.now()}-${Math.random()}`,
            type: 'path',
            paths: serializablePaths,
          };
          setMemorizedPosition([newPathEntry]);
        } else {
          console.log('No paths to save');
        }
      } catch (error) {
        console.error('Error saving path:', JSON.stringify(error));
      }
    };

    const toggleMode = (newMode: 'draw' | 'erase') => {
      setEraseMode(newMode === 'erase');
    };

    useImperativeHandle(ref, () => ({
      Undo: handleUndo,
      Redo: handleRedo,
      handleOkay: async () => {
        await handleOkay();
      },
      ToggleEraseMode: (value) => {
        toggleMode(value ? 'erase' : 'draw');
      },
    }));

    // Always render saved paths, even when not in drawing mode
    if (!visible && (!paintData || paintData.length === 0)) return null;

    return (
      <Canvas
        style={[styles.canvas, { zIndex: visible ? 110 : 10 }]}
        pointerEvents={visible ? 'auto' : 'none'}
        onTouchStart={onTouchHandler}
        onTouchMove={onTouchHandler}
        onTouchEnd={onTouchHandler}
      >
        {paths.map((pathItem, index) => (
          <Path
            key={`${pathItem.id}-${index}`}
            path={pathItem.path}
            color={pathItem.isErase ? 'transparent' : pathItem.color}
            style="stroke"
            strokeWidth={pathItem.size}
            strokeCap="round"
            strokeJoin="round"
            blendMode={pathItem.isErase ? 'clear' : 'srcOver'}
          />
        ))}

        {currentPath && (
          <Path
            path={currentPath.path}
            color={currentPath.isErase ? 'transparent' : currentPath.color}
            style="stroke"
            strokeWidth={currentPath.size}
            strokeCap="round"
            strokeJoin="round"
            blendMode={currentPath.isErase ? 'clear' : 'srcOver'}
          />
        )}
      </Canvas>
    );
  },
);

const styles = StyleSheet.create({
  canvas: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  iconbackground: {
    backgroundColor: '#0000004D',
    height: hp(5),
    width: hp(5),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 40,
  },
});

export default React.memo(PaintBrush);
